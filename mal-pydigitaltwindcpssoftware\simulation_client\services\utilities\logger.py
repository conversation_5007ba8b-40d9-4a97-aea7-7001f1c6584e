#!/usr/bin/env python3
"""
Logging Utilities
Centralized logging configuration and utilities
"""

import logging
import sys
from typing import Optional
from configuration.settings import config


def setup_logging(level: Optional[str] = None, format_string: Optional[str] = None) -> logging.Logger:
    """
    Setup logging configuration
    
    Args:
        level: Logging level (defaults to config.LOG_LEVEL)
        format_string: Log format string (defaults to config.LOG_FORMAT)
    
    Returns:
        Configured logger instance
    """
    log_level = level or config.LOG_LEVEL
    log_format = format_string or config.LOG_FORMAT
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Get logger for the application
    logger = logging.getLogger("technomatix_ml_integration")
    logger.info(f"Logging configured with level: {log_level}")
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name"""
    return logging.getLogger(name)


class LoggerMixin:
    """Mixin class to add logging capability to any class"""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class"""
        return logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
