#!/usr/bin/env python3
"""
Main Application for Technomatix-AAS Integration
Monitors Technomatix simulation and updates processing times from AAS server
"""

import sys
import os
import time
import logging
import re
from typing import Set

# Add main directory to path
sys.path.append(os.path.dirname(__file__))

from main.config import Config
from main.technomatix_monitor import TechnomatixMonitor, StationStatus
from main.aas_client import AASClient

def setup_logging(config: Config):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL),
        format=config.LOG_FORMAT
    )

def extract_product_info(contents_list_str: str) -> list:
    """
    Extract product information from contentsList string
    
    Args:
        contents_list_str: String representation of contentsList
        
    Returns:
        List of product dictionaries with id and name
    """
    products = []
    
    if contents_list_str and isinstance(contents_list_str, str):
        # Look for patterns like "Part:123"
        part_matches = re.findall(r'Part:(\d+)', contents_list_str)
        
        for part_number in part_matches:
            products.append({
                'id': f"Part:{part_number}",
                'name': f"Part:{part_number}"
            })
    
    return products

def monitor_simulation(config: Config):
    """
    Main monitoring function
    
    Args:
        config: Configuration object
    """
    logger = logging.getLogger(__name__)
    
    # Initialize components
    monitor = TechnomatixMonitor()
    aas_client = AASClient()
    
    try:
        # Step 1: Connect to Technomatix
        print("\n1. Connecting to Technomatix...")
        if not monitor.connect():
            print(" Failed to connect to Technomatix")
            return 1
        print(" Connected to Technomatix")
        
        # Step 2: Test AAS connection
        print("\n2. Testing AAS server connection...")
        if not aas_client.test_connection():
            print("⚠  AAS server not reachable, will use fallback values")
        else:
            print(" AAS server connected")
        
        # Step 3: Start simulation
        print("\n3. Starting simulation via EventController...")
        if not monitor.start_simulation():
            print(" Failed to start simulation")
            return 1
        print(" Simulation started")
        
        # Step 4: Monitor for products
        print(f"\n4. Monitoring for part arrivals at Station1...")
        print(f"   (Will monitor for {config.SIMULATION_MONITOR_TIME} seconds)")
        print("   Product Counter | Product Name | Status | Processing Time")
        print("   " + "-" * 60)
        
        start_time = time.time()
        product_counter = 0
        processed_products: Set[str] = set()
        
        while (time.time() - start_time) < config.SIMULATION_MONITOR_TIME:
            current_status = monitor.get_station1_status()
            
            # Check if there are parts in the station
            if current_status == StationStatus.PRODUCT_ARRIVED:
                station_info = monitor.get_station_info()
                contents_list = station_info['attributes'].get('contentsList', '')
                
                # Extract product information
                products = extract_product_info(contents_list)
                
                # Process each product
                for product in products:
                    part_id = product['id']
                    part_name = product['name']
                    
                    # If this is a new part we haven't processed yet
                    if part_id not in processed_products:
                        product_counter += 1
                        processed_products.add(part_id)
                        
                        elapsed = time.time() - start_time
                        print(f"   #{product_counter:03d}          | {part_name:12s} | ARRIVED | [{elapsed:.1f}s]")
                        
                        # Get processing time from AAS
                        aas_processing_time = aas_client.get_production_time_with_fallback()
                        
                        # Update Technomatix
                        if monitor.update_processing_time(aas_processing_time):
                            print(f"   #{product_counter:03d}          | {part_name:12s} | UPDATED | {aas_processing_time} sec")
                        else:
                            print(f"   #{product_counter:03d}          | {part_name:12s} | ERROR   | Failed to update")
            
            # Clean up processed_products set when station becomes empty
            elif current_status == StationStatus.EMPTY:
                if processed_products:  # Only clear if we had products
                    processed_products.clear()
            
            time.sleep(config.PRODUCT_CHECK_INTERVAL)
        
        print(f"\n Monitoring completed (timeout after {config.SIMULATION_MONITOR_TIME}s)")
        print(f" Total products processed: {product_counter}")
        
        # Step 5: Stop simulation
        print("\n5. Stopping simulation...")
        monitor.stop_simulation()
        print(" Simulation stopped")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n Monitoring interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Error during monitoring: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        # Cleanup
        print("\n6. Cleaning up...")
        try:
            monitor.stop_simulation()
        except:
            pass
        monitor.disconnect()
        print("Disconnected from Technomatix")

def main():
    """Main entry point"""
    print("=== Technomatix-AAS Integration ===")
    print("Monitoring Technomatix simulation with AAS processing time updates")
    
    # Load and validate configuration
    config = Config()
    
    # Validate configuration
    errors = config.validate()
    if errors:
        print(" Configuration errors:")
        for error in errors:
            print(f"   - {error}")
        return 1
    
    # Print configuration
    config.print_config()
    
    # Setup logging
    setup_logging(config)
    
    # Start monitoring
    return monitor_simulation(config)

if __name__ == "__main__":
    sys.exit(main())
