#!/usr/bin/env python3
"""
Technomatix-ML Integration Main Orchestrator
Coordinates the complete workflow: AAS metadata -> ML prediction -> Technomatix update

This is the main entry point that orchestrates:
1. AAS metadata management (currently hardcoded, future: from AAS server)
2. ML model management and predictions
3. Technomatix simulation integration
4. Complete workflow coordination

Usage:
    python main.py
"""

import sys
import os
import time
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import modules
from config.settings import config
from src.utils.logger import setup_logging
from src.simulation.monitor import SimulationMonitor
from src.aas.client import AASClient
from src.ml.predictor import MLPredictor


class TechnomatixMLOrchestrator:
    """Main orchestrator for Technomatix-ML integration"""
    
    def __init__(self, use_aas: bool = True):
        """Initialize the orchestrator"""
        self.logger = logging.getLogger(__name__)
        self.use_aas = use_aas

        # Initialize components with AAS integration
        self.aas_client = AASClient() if use_aas else None
        self.ml_predictor = MLPredictor(aas_client=self.aas_client)
        self.simulation_monitor = SimulationMonitor(aas_client=self.aas_client)

        # Status tracking
        self.is_initialized = False
        
    def initialize(self) -> bool:
        """Initialize all components and validate configuration"""
        try:
            # Validate configuration
            errors = config.validate()
            if errors:
                self.logger.error("Configuration validation failed:")
                for error in errors:
                    self.logger.error(f"  - {error}")
                return False

            # Test AAS integration if enabled
            # Test AAS integration
            self.logger.info("Testing AAS integration...")
            if self.aas_client:
                aas_validation = self.aas_client.validate_aas_connection()
                if aas_validation.get("connection_status"):
                    self.logger.info("✓ AAS server connected")
                else:
                    self.logger.warning("✗ AAS server connection failed")
            else:
                self.logger.info("AAS integration disabled")

            # Test ML service connection
            ml_validation = self.ml_predictor.validate_configuration()
            if ml_validation["ml_service_reachable"]:
                self.logger.info("✓ ML service connected")
            else:
                self.logger.warning("✗ ML service not available - will use default processing times")

            # Display metadata summary
            if self.aas_client:
                metadata = self.aas_client.get_metadata_structure()
                if metadata:
                    self.logger.info(f"Configuration: {metadata.get('part_attributes')} → {metadata.get('prediction_target')}")
                    self.logger.info(f"✓ Using AAS metadata from {metadata.get('ml_endpoint')}")
                else:
                    self.logger.warning("No metadata available from AAS")
            else:
                self.logger.info("Using default configuration")
            
            self.is_initialized = True
            self.logger.info("Orchestrator initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize orchestrator: {e}")
            return False
    
    def run_integration_workflow(self) -> int:
        """Run the complete AAS→Simulation→ML→Simulation workflow"""
        if not self.is_initialized:
            self.logger.error("Orchestrator not initialized")
            return 1

        print("=" * 80)
        print("TECHNOMATIX-AAS-ML INTEGRATION - STARTING COMPLETE WORKFLOW")
        print("=" * 80)

        try:
            # Step 1: Display AAS metadata
            print("-" * 60)
            self.logger.info("STEP 1: AAS METADATA SUMMARY")
            print("-" * 60)
            if self.aas_client:
                metadata = self.aas_client.get_metadata_structure()
                if metadata:
                    self.logger.info(f"   ML Endpoint: {metadata.get('ml_endpoint', 'N/A')}")
                    self.logger.info(f"   Inputs: {metadata.get('part_attributes', [])}")
                    self.logger.info(f"   Output: {metadata.get('prediction_target', 'N/A')}")
                else:
                    self.logger.info("   No metadata available from AAS")
            else:
                self.logger.info("   AAS integration disabled")

            # Step 2: Connect to Technomatix
            print("-" * 60)
            self.logger.info("STEP 2: CONNECTING TO TECHNOMATIX")
            print("-" * 60)
            if not self.simulation_monitor.connect():
                self.logger.error("ERROR: Failed to connect to Technomatix")
                return 1
            self.logger.info("SUCCESS: Connected to Technomatix")

            # Step 3: Start simulation
            print("-" * 60)
            self.logger.info("STEP 3: STARTING SIMULATION")
            print("-" * 60)
            if not self.simulation_monitor.start_simulation():
                self.logger.error("ERROR: Failed to start simulation")
                return 1
            self.logger.info("SUCCESS: Simulation started")

            # Step 4: Run monitoring workflow
            print("-" * 60)
            self.logger.info("STEP 4: STARTING MONITORING WORKFLOW")
            print("-" * 60)
            self.logger.info("Workflow: Part Detection -> Feature Extraction -> ML Prediction -> Update Simulation")
            self.logger.info("Press Ctrl+C to stop monitoring")
            print()

            # Run the monitoring loop
            self.run_monitoring_loop()
            return 0

        except KeyboardInterrupt:
            print("-" * 60)
            self.logger.info("WORKFLOW INTERRUPTED BY USER")
            print("-" * 60)
            return 0
        except Exception as e:
            print("-" * 60)
            self.logger.error(f"SYSTEM ERROR: {e}")
            print("-" * 60)
            import traceback
            traceback.print_exc()
            return 1
        finally:
            # Cleanup
            print("-" * 60)
            self.logger.info("SHUTTING DOWN SYSTEM")
            print("-" * 60)
            try:
                self.simulation_monitor.stop_simulation()
            except:
                pass
            self.simulation_monitor.disconnect()
            self.logger.info("Cleanup completed")

    def run_monitoring_loop(self):
        """Main monitoring loop - Complete workflow for each part"""
        while True:
            try:
                # Wait for product arrival
                self.logger.info("Waiting for product at Station1...")
                if self.simulation_monitor.wait_for_product_arrival(check_interval=1.0, timeout=30.0):
                    print("." * 40)
                    self.logger.info("PRODUCT DETECTED - Starting workflow")
                    print("." * 40)

                    # Complete workflow
                    self.process_complete_workflow()

                    # Brief pause
                    time.sleep(2.0)
                else:
                    self.logger.info("No product in timeout period, continuing monitoring...")

            except KeyboardInterrupt:
                print("-" * 60)
                self.logger.info("MONITORING LOOP STOPPED")
                print("-" * 60)
                break
            except Exception as e:
                self.logger.error(f"MONITORING ERROR: {e}")
                time.sleep(5.0)

    def process_complete_workflow(self):
        """Complete workflow: Extract -> Predict -> Update"""
        try:
            # Step 1: Extract features from simulation
            self.logger.info("STEP A: Extracting part dimensions from simulation")
            features = self.simulation_monitor.extract_part_features()

            if not features:
                self.logger.warning("WARNING: No features extracted, skipping workflow")
                return

            self.logger.info(f"EXTRACTED: Width={features.get('Width')}, Thickness={features.get('Thickness')}")

            # Step 2: Get ML prediction
            self.logger.info("STEP B: Sending to ML service for prediction")
            prediction = self.ml_predictor.get_prediction(features)

            if prediction is None:
                self.logger.warning("WARNING: ML prediction failed, using default")
                return

            self.logger.info(f"ML PREDICTION: ProcessingTime={prediction}")

            # Step 3: Update simulation
            self.logger.info("STEP C: Updating Station1 processing time")
            if self.simulation_monitor.update_processing_time(prediction):
                self.logger.info("SUCCESS: Processing time applied to simulation")
                self.logger.info(f"RESULT: Station1 now using ProcessingTime={prediction} for this part")
            else:
                self.logger.warning("WARNING: Failed to update processing time")

        except Exception as e:
            self.logger.error(f"WORKFLOW ERROR: {e}")

    def run_test_mode(self) -> int:
        """Run in test mode without full simulation"""
        if not self.is_initialized:
            self.logger.error("Orchestrator not initialized")
            return 1
        
        try:
            self.logger.info("Running in test mode...")
            
            # Test metadata
            self.logger.info("Testing AAS metadata...")
            if self.aas_client:
                metadata = self.aas_client.get_metadata_structure()
                self.logger.info(f"Metadata: {metadata}")
            else:
                self.logger.info("AAS integration disabled")
            
            # Test ML prediction with AAS metadata
            self.logger.info("Testing ML prediction...")
            if self.aas_client:
                metadata = self.aas_client.get_metadata_structure()
                if metadata and metadata.get('part_attributes'):
                    # Create test features based on AAS metadata
                    test_features = {}
                    for attr in metadata['part_attributes']:
                        test_features[attr] = 1.0  # Use 1.0 as test value

                    prediction = self.ml_predictor.get_prediction(test_features)
                    if prediction:
                        self.logger.info(f"ML prediction successful: {prediction}")
                    else:
                        self.logger.warning("ML prediction failed")
                else:
                    self.logger.warning("Cannot test ML prediction without AAS metadata")
            else:
                self.logger.warning("Cannot test ML prediction without AAS client")
            
            # Test Technomatix connection (without starting simulation)
            self.logger.info("Testing Technomatix connection...")
            if self.simulation_monitor.connect():
                self.logger.info("Technomatix connection successful")
                status = self.simulation_monitor.get_status()
                self.logger.info(f"Monitor status: {status}")
                self.simulation_monitor.disconnect()
            else:
                self.logger.error("Technomatix connection failed")
                return 1
            
            self.logger.info("Test mode completed successfully")
            return 0
            
        except Exception as e:
            self.logger.error(f"Error during test mode: {e}")
            return 1


def main():
    """Main entry point"""
    print("=" * 70)
    print("Technomatix-ML Integration Orchestrator")
    print("=" * 70)
    print()
    
    try:
        # Setup logging
        setup_logging()
        
        # Check for AAS disable flag
        use_aas = "--no-aas" not in sys.argv

        # Create orchestrator
        orchestrator = TechnomatixMLOrchestrator(use_aas=use_aas)

        if not use_aas:
            print("AAS integration disabled - using hardcoded configuration")

        # Initialize
        if not orchestrator.initialize():
            print("Failed to initialize orchestrator")
            return 1

        # Check command line arguments for test mode
        if "--test" in sys.argv:
            return orchestrator.run_test_mode()
        else:
            return orchestrator.run_integration_workflow()
        
    except ImportError as e:
        print("Missing required packages:")
        print(f"   {e}")
        print()
        print("Please run: pip install -r requirements.txt")
        return 1
    except Exception as e:
        print("Unexpected error occurred:")
        print(f"   {e}")
        print()
        print("Please check your configuration and try again")
        print("Run with --test flag for basic testing")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
