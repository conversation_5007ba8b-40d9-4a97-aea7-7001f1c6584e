#!/usr/bin/env python3
"""
ML Model Manager
Handles ML model lifecycle, parameters, and configuration
Now supports dynamic configuration from AAS metadata
"""

import logging
from typing import Dict, Any, Optional, List
from configuration.settings import config


class ModelManager:
    """Manages ML model configuration and parameters"""

    def __init__(self, aas_client=None):
        self.logger = logging.getLogger(__name__)
        self.aas_client = aas_client
        self._model_config = self._initialize_model_config()

    def _initialize_model_config(self) -> Dict[str, Any]:
        """Initialize model configuration from AAS metadata"""
        if not self.aas_client:
            raise ValueError("Model Manager requires AAS client for configuration")

        metadata = self.aas_client.get_metadata_structure()
        if not metadata:
            raise ValueError("Cannot initialize model without AAS metadata")

        input_features = metadata.get('part_attributes')
        output_target = metadata.get('prediction_target')

        if not input_features:
            raise ValueError("No input features available from AAS metadata")
        if not output_target:
            raise ValueError("No output target available from AAS metadata")

        # No artificial range restrictions - accept any reasonable physical values
        feature_scaling = {}
        for feature in input_features:
            # Accept any positive values for physical dimensions
            feature_scaling[feature] = {"min": 0.0, "max": float('inf')}

        return {
            "model_type": "regression",
            "input_features": input_features,
            "output_target": output_target,
            "feature_scaling": feature_scaling,
            "prediction_bounds": {
                "min": 1.0,  # minimum processing time
                "max": 60.0  # maximum processing time
            }
        }
    
    def get_model_parameters(self) -> Dict[str, Any]:
        """Get current model parameters"""
        self.logger.debug("Retrieved model parameters")
        return self._model_config.copy()
    
    def get_input_features(self) -> List[str]:
        """Get list of input features for the model"""
        features = self._model_config.get("input_features", [])
        self.logger.debug(f"Model input features: {features}")
        return features
    
    def get_output_target(self) -> str:
        """Get model output target"""
        target = self._model_config.get("output_target", "ProcessingTime")
        self.logger.debug(f"Model output target: {target}")
        return target
    
    def validate_input_features(self, features: Dict[str, float]) -> bool:
        """Validate input features against model requirements"""
        required_features = self.get_input_features()
        
        # Check if all required features are present
        missing_features = []
        for feature in required_features:
            if feature not in features:
                missing_features.append(feature)
        
        if missing_features:
            self.logger.warning(f"Missing required features: {missing_features}")
            return False
        
        # Check feature value ranges
        scaling_config = self._model_config.get("feature_scaling", {})
        for feature, value in features.items():
            if feature in scaling_config:
                min_val = scaling_config[feature].get("min", float('-inf'))
                max_val = scaling_config[feature].get("max", float('inf'))
                
                if not (min_val <= value <= max_val):
                    self.logger.warning(f"Feature {feature} value {value} outside range [{min_val}, {max_val}]")
                    return False
        
        self.logger.debug("Input features validated successfully")
        return True
    
    def normalize_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """Normalize features - no normalization needed since we accept any values"""
        # Since we removed artificial ranges, just return features as-is
        # The ML service will handle any normalization it needs internally
        self.logger.debug(f"Features passed through without normalization: {features}")
        return features.copy()
    
    def validate_prediction(self, prediction: float) -> float:
        """Validate and bound prediction value"""
        bounds = self._model_config.get("prediction_bounds", {})
        min_val = bounds.get("min", 0.0)
        max_val = bounds.get("max", float('inf'))
        
        # Bound the prediction
        bounded_prediction = max(min_val, min(prediction, max_val))
        
        if bounded_prediction != prediction:
            self.logger.warning(f"Prediction {prediction} bounded to {bounded_prediction}")
        
        return bounded_prediction
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive model information"""
        return {
            "model_type": self._model_config.get("model_type"),
            "input_features": self.get_input_features(),
            "output_target": self.get_output_target(),
            "feature_count": len(self.get_input_features()),
            "scaling_enabled": bool(self._model_config.get("feature_scaling")),
            "prediction_bounds": self._model_config.get("prediction_bounds")
        }
    
    def update_model_config(self, new_config: Dict[str, Any]) -> bool:
        """Update model configuration"""
        try:
            self._model_config.update(new_config)
            self.logger.info("Model configuration updated successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to update model configuration: {e}")
            return False
