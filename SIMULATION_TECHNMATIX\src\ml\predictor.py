#!/usr/bin/env python3
"""
ML Predictor Service
Handles communication with ML prediction service and manages predictions
Integrates with AAS metadata for dynamic endpoint configuration
"""

import logging
import requests
import json
from typing import Dict, Optional, Any
from config.settings import config
from .model_manager import ModelManager
from ..aas.client import AASClient


class MLPredictor:
    """Handles ML predictions and service communication"""

    def __init__(self, aas_client: Optional[AASClient] = None):
        self.logger = logging.getLogger(__name__)
        self.aas_client = aas_client
        self.model_manager = ModelManager(aas_client=aas_client)

        # Get service URL from AAS - fail if not available
        self.service_url = self._get_service_url()
        if not self.service_url:
            raise ValueError("Cannot initialize ML Predictor without AAS metadata")

        self.timeout = config.ML_REQUEST_TIMEOUT
        self.retry_attempts = config.ML_RETRY_ATTEMPTS

        self.logger.debug(f"ML Predictor initialized with endpoint: {self.service_url}")

    def _get_service_url(self) -> Optional[str]:
        """Get ML service URL from AAS metadata"""
        if self.aas_client:
            metadata = self.aas_client.get_metadata_structure()
            if metadata and metadata.get('ml_endpoint'):
                aas_endpoint = metadata['ml_endpoint']
                if aas_endpoint.startswith(('http://', 'https://')):
                    # Use AAS endpoint, add prediction path if needed
                    if not aas_endpoint.endswith('/predict'):
                        return f"{aas_endpoint}/predict"
                    return aas_endpoint

        # No fallback - system requires AAS metadata
        self.logger.error("No ML service URL available from AAS metadata - Cannot proceed")
        return None
        
    def test_connection(self) -> bool:
        """Test connection to ML service"""
        try:
            # Extract base URL from service_url and add health endpoint
            base_url = self.service_url.replace('/predict', '')
            health_url = f"{base_url}/health"
            response = requests.get(health_url, timeout=self.timeout)

            if response.status_code == 200:
                self.logger.debug("ML service connection successful")
                return True
            else:
                self.logger.warning(f"ML service returned status {response.status_code}")
                return False

        except requests.exceptions.RequestException as e:
            self.logger.warning(f"ML service connection failed: {e}")
            return False
    
    def get_prediction(self, features: Dict[str, float]) -> Optional[float]:
        """Get prediction from ML service"""
        # Validate input features
        if not self.model_manager.validate_input_features(features):
            self.logger.error("Invalid input features for prediction")
            return None
        
        # Normalize features if needed
        normalized_features = self.model_manager.normalize_features(features)
        
        # Attempt prediction with retries
        for attempt in range(self.retry_attempts + 1):
            try:
                prediction = self._make_prediction_request(normalized_features)
                if prediction is not None:
                    # Validate and bound prediction
                    validated_prediction = self.model_manager.validate_prediction(prediction)
                    self.logger.info(f"ML Prediction: {list(features.values())} -> {validated_prediction}")
                    return validated_prediction
                    
            except Exception as e:
                self.logger.warning(f"Prediction attempt {attempt + 1} failed: {e}")
                if attempt == self.retry_attempts:
                    self.logger.error("All prediction attempts failed")
        
        return None
    
    def _make_prediction_request(self, features: Dict[str, float]) -> Optional[float]:
        """Make actual prediction request to ML service"""
        try:
            # Send features as provided by AAS metadata - no hardcoded mapping
            ml_features = features.copy()

            # ML service expects features nested under "features" key
            payload = {
                "features": ml_features
            }

            self.logger.debug(f"Sending to ML service: {payload}")

            response = requests.post(
                self.service_url,
                json=payload,
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                prediction = result.get("prediction")
                
                if prediction is not None:
                    return float(prediction)
                else:
                    self.logger.error("No prediction in response")
                    return None
            else:
                self.logger.error(f"ML service error: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request to ML service failed: {e}")
            return None
        except (ValueError, KeyError, TypeError) as e:
            self.logger.error(f"Error parsing ML service response: {e}")
            return None
    
    def get_test_prediction(self, test_features: Dict[str, float]) -> Optional[float]:
        """Get test prediction using provided features"""
        self.logger.info(f"Getting test prediction with features: {test_features}")
        return self.get_prediction(test_features)
    
    def batch_predict(self, feature_list: list[Dict[str, float]]) -> list[Optional[float]]:
        """Get predictions for multiple feature sets"""
        predictions = []
        
        for i, features in enumerate(feature_list):
            self.logger.debug(f"Processing batch prediction {i + 1}/{len(feature_list)}")
            prediction = self.get_prediction(features)
            predictions.append(prediction)
        
        self.logger.info(f"Batch prediction completed: {len(predictions)} predictions")
        return predictions
    
    def refresh_endpoint(self) -> bool:
        """Refresh ML service endpoint from AAS"""
        try:
            # Get new URL from AAS
            new_url = self._get_service_url()
            if new_url != self.service_url:
                old_url = self.service_url
                self.service_url = new_url
                self.logger.info(f"ML endpoint updated from {old_url} to {new_url}")
                return True
            else:
                self.logger.debug("ML endpoint unchanged after refresh")
                return True
        except Exception as e:
            self.logger.error(f"Error refreshing endpoint: {e}")
            return False

    def get_service_info(self) -> Dict[str, Any]:
        """Get information about the ML service including AAS integration status"""
        metadata_info = None
        if self.aas_client:
            metadata_info = self.aas_client.get_metadata_structure()

        service_info = {
            "service_url": self.service_url,
            "timeout": self.timeout,
            "retry_attempts": self.retry_attempts,
            "model_info": self.model_manager.get_model_info(),
            "connection_status": self.test_connection(),
            "metadata_summary": metadata_info
        }

        return service_info

    def validate_configuration(self) -> Dict[str, Any]:
        """Validate ML predictor configuration - requires AAS integration"""
        validation = {
            "ml_service_reachable": self.test_connection() if self.service_url else False,
            "service_url": self.service_url,
            "input_parameters": [],
            "output_parameter": None,
            "errors": []
        }

        if not self.aas_client:
            validation["errors"].append("AAS client not available")
            return validation

        metadata = self.aas_client.get_metadata_structure()
        if not metadata:
            validation["errors"].append("AAS metadata not available")
            return validation

        validation["input_parameters"] = metadata.get('part_attributes', [])
        validation["output_parameter"] = metadata.get('prediction_target')

        # Check if we have required parameters from AAS
        if not validation["input_parameters"]:
            validation["errors"].append("No input parameters available from AAS")

        if not validation["output_parameter"]:
            validation["errors"].append("No output parameter available from AAS")

        if not validation["ml_service_reachable"]:
            validation["errors"].append("ML service not reachable")

        return validation
