#!/usr/bin/env python3
"""
Test B: Technomatix Connection
Tests if we can connect to Technomatix and pull data from simulation
"""

import sys
import os
import logging

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from main.config import Config
from main.technomatix_monitor import TechnomatixMonitor

def main():
    print("=== Test B: Technomatix Connection ===")
    print("Testing basic connection and value reading...")
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    config = Config()
    monitor = TechnomatixMonitor(config.MODEL_PATH)
    
    try:
        # Test 1: Connection
        print("\n1. Connecting to Technomatix...")
        if not monitor.connect():
            print(" Failed to connect to Technomatix")
            return 1
        print(" Connected successfully!")
        
        # Test 2: Read Station1 values
        print("\n2. Reading Station1 values...")
        station_info = monitor.get_station_info()
        
        print(f"   Station Status: {station_info['status']}")
        print(f"   Processing Time: {station_info.get('processing_time', 'N/A')}")
        print(f"   Attributes: {station_info.get('attributes', {})}")
        
        # Test 3: Start simulation
        print("\n3. Testing simulation start...")
        if monitor.start_simulation():
            print(" Simulation started successfully!")
            
            # Test 4: Check values after start
            print("\n4. Checking values after simulation start...")
            import time
            time.sleep(3)  # Wait a bit for simulation to start
            
            station_info = monitor.get_station_info()
            print(f"   Station Status: {station_info['status']}")
            print(f"   Attributes: {station_info.get('attributes', {})}")
            
            # Stop simulation
            monitor.stop_simulation()
            print(" Simulation stopped")
        else:
            print(" Failed to start simulation")
            return 1
        
        print("\n Test B completed successfully!")
        return 0
        
    except Exception as e:
        print(f" Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        monitor.disconnect()
        print("Disconnected from Technomatix")

if __name__ == "__main__":
    sys.exit(main())
