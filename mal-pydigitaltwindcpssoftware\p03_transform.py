import pandas as pd
from p02_select import select

def transform():

    # get shop floor data from select()
    df=select()

    ## ---construct---

    # aggregate production time per record
    df_production_records = df.groupby('Auftrag').agg(
        production_time=('Maschinenlaufzeit IST', 'sum'),
        factor_1=('Breite Produktionsartikel', 'first'),
        factor_2=('Dicke Produktionsartikel', 'first')
    ).reset_index()
    
    # transform names to structured data
    df_production_records = df_production_records.rename(columns={'Auftrag':'record'})

    # export dataframe to csv
    df_production_records.to_csv('data/production_records.csv', index=False)

transform()