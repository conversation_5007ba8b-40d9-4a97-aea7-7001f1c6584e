import pandas as pd

def connect():

    ## ---integrate---

    # import csv into dataframe
    df = pd.read_csv(
        'data/shop_floor_data_recording.csv',
        sep=';',
        decimal=',',
        encoding='ISO-8859-1',
        low_memory=False,
        on_bad_lines='skip')
    
    ## ---format---

    # transform to numeric, 0 when not a numeric data
    columns_to_convert = ['Breite Produktionsartikel', 'Dicke Produktionsartikel']    
    for col in columns_to_convert:
        df[col] = pd.to_numeric(
            df[col].str.replace(',', '.', regex=False),
            errors='coerce'
        ).fillna(0)

    # return dataframe when integrate() is called
    return df