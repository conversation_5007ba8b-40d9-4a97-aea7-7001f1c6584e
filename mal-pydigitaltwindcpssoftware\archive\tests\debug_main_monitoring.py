#!/usr/bin/env python3
"""
Debug what the main monitoring code is actually getting
"""

import sys
import os
import re

# Add the main directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from main.technomatix_monitor import TechnomatixMonitor, StationStatus
from main.config import Config

def extract_product_info(contents_list_str: str) -> list:
    """
    Extract product information from contentsList string
    (Same function as in main.py)
    """
    products = []
    
    if contents_list_str and isinstance(contents_list_str, str):
        # Look for patterns like "Part:123"
        part_matches = re.findall(r'Part:(\d+)', contents_list_str)
        
        for part_number in part_matches:
            products.append({
                'id': f"Part:{part_number}",
                'name': f"Part:{part_number}"
            })
    
    return products

def debug_main_monitoring():
    """Debug what main monitoring code sees"""
    print("=== Debug Main Monitoring Logic ===")
    
    monitor = TechnomatixMonitor()
    
    try:
        # Connect
        print("1. Connecting...")
        if not monitor.connect():
            print(" Failed to connect")
            return
        print(" Connected")
        
        # Start simulation
        print("\n2. Starting simulation...")
        monitor.start_simulation()
        print(" Started")
        
        # Check status
        print("\n3. Checking status...")
        current_status = monitor.get_station1_status()
        print(f"Status: {current_status}")
        
        if current_status == StationStatus.PRODUCT_ARRIVED:
            print("\n4. Getting station info (same as main.py)...")
            station_info = monitor.get_station_info()
            
            print("Full station_info:")
            for key, value in station_info.items():
                print(f"  {key}: {value}")
            
            print(f"\nAttributes section:")
            for key, value in station_info['attributes'].items():
                print(f"  {key}: {value} (type: {type(value)})")
            
            # This is exactly what main.py does
            contents_list = station_info['attributes'].get('contentsList', '')
            print(f"\ncontentsList from station_info: '{contents_list}'")
            print(f"Type: {type(contents_list)}")
            
            # Test the extraction
            products = extract_product_info(contents_list)
            print(f"Extracted products: {products}")
            
            if not products:
                print("\n DEBUGGING: Why no products extracted?")
                print(f"contents_list value: {repr(contents_list)}")
                print(f"Is string? {isinstance(contents_list, str)}")
                if isinstance(contents_list, str):
                    matches = re.findall(r'Part:(\d+)', contents_list)
                    print(f"Regex matches: {matches}")
                    
                    # Try different patterns
                    all_matches = re.findall(r'(\w+):(\d+)', contents_list)
                    print(f"All word:number patterns: {all_matches}")
        else:
            print(" No product detected - can't debug extraction")
            
        # Also test direct attribute access
        print(f"\n5. Direct attribute access...")
        try:
            contents_path = f"{monitor.station1_path}.{monitor.station1_contents_list_attr}"
            direct_contents = monitor.ps.GetValue(contents_path)
            print(f"Direct contentsList: {direct_contents} (type: {type(direct_contents)})")
            
            # Test extraction on direct value
            if isinstance(direct_contents, str):
                direct_products = extract_product_info(direct_contents)
                print(f"Products from direct access: {direct_products}")
            else:
                # Convert to string and test
                str_contents = str(direct_contents)
                print(f"Converted to string: '{str_contents}'")
                direct_products = extract_product_info(str_contents)
                print(f"Products from converted string: {direct_products}")
                
        except Exception as e:
            print(f"Error with direct access: {e}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        monitor.disconnect()

if __name__ == "__main__":
    debug_main_monitoring()
