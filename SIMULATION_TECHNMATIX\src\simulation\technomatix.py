#!/usr/bin/env python3
"""
Technomatix Integration Module
Handles connection and communication with Technomatix Plant Simulation
"""

import win32com.client
import pythoncom
import logging
import time
from typing import Optional, Dict, Any, Tuple
from enum import Enum
from config.settings import config


class StationStatus(Enum):
    """Enumeration for station status"""
    EMPTY = "empty"
    PRODUCT_ARRIVED = "product_arrived"
    PROCESSING = "processing"
    UNKNOWN = "unknown"


class TechnomatixConnector:
    """Handles connection and basic operations with Technomatix Plant Simulation"""
    
    def __init__(self, model_path: Optional[str] = None):
        """Initialize Technomatix connector"""
        self.model_path = model_path or config.MODEL_PATH
        self.ps = None
        self.logger = logging.getLogger(__name__)
        self.is_connected = False
        
        # Station configuration
        self.station_name = config.STATION_NAME
        
    def connect(self) -> bool:
        """Connect to Technomatix Plant Simulation and load model"""
        try:
            pythoncom.CoInitialize()
            self.ps = win32com.client.Dispatch("Tecnomatix.PlantSimulation.RemoteControl")
            self.ps.SetTrustModels(True)
            
            if not self.model_path:
                raise ValueError("Model path is required for proper operation")
            
            self.logger.info(f"Loading model: {self.model_path}")
            self.ps.LoadModel(self.model_path)
            self.ps.SetVisible(True)
            
            self.is_connected = True
            self.logger.info("Successfully connected to Technomatix and loaded model")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to Technomatix: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """Disconnect from Technomatix"""
        if self.ps:
            try:
                self.stop_simulation()
                self.ps = None
                self.is_connected = False
                self.logger.info("Disconnected from Technomatix")
            except Exception as e:
                self.logger.error(f"Error during disconnect: {e}")
    
    def start_simulation(self) -> bool:
        """Start the simulation"""
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return False
        
        try:
            # Use EventController to start simulation
            simtalk_cmd = ".Models.Model.EventController.start(true)"
            self.ps.ExecuteSimTalk(simtalk_cmd)
            self.logger.info("Simulation started")
            return True
        except Exception as e:
            self.logger.error(f"Failed to start simulation: {e}")
            return False
    
    def stop_simulation(self) -> bool:
        """Stop the simulation"""
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return False
        
        try:
            simtalk_cmd = ".Models.Model.EventController.stop(true)"
            self.ps.ExecuteSimTalk(simtalk_cmd)
            self.logger.info("Simulation stopped")
            return True
        except Exception as e:
            self.logger.error(f"Failed to stop simulation: {e}")
            return False
    
    def reset_simulation(self) -> bool:
        """Reset the simulation"""
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return False
        
        try:
            simtalk_cmd = ".Models.Model.EventController.reset(true)"
            self.ps.ExecuteSimTalk(simtalk_cmd)
            self.logger.info("Simulation reset")
            return True
        except Exception as e:
            self.logger.error(f"Failed to reset simulation: {e}")
            return False
    
    def execute_simtalk(self, command: str) -> Any:
        """Execute a SimTalk command"""
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return None
        
        try:
            result = self.ps.ExecuteSimTalk(command)
            self.logger.debug(f"SimTalk command executed: {command}")
            return result
        except Exception as e:
            self.logger.error(f"Failed to execute SimTalk command '{command}': {e}")
            return None
    
    def get_value(self, object_path: str, attribute: str) -> Any:
        """Get value of an object attribute using GetValue (like igcv-i30)"""
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return None

        try:
            # Use GetValue method like igcv-i30 reference
            full_path = f"{object_path}.{attribute}"
            result = self.ps.GetValue(full_path)
            self.logger.debug(f" GetValue: {full_path} = {result}")
            return result
        except Exception as e:
            self.logger.debug(f" GetValue failed: {object_path}.{attribute} - {e}")
            return None
    
    def set_value(self, object_path: str, attribute: str, value: Any) -> bool:
        """Set value of an object attribute using SetValue (like igcv-i30)"""
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return False

        try:
            # Use SetValue method like igcv-i30 reference
            full_path = f"{object_path}.{attribute}"
            self.ps.SetValue(full_path, value)
            self.logger.debug(f" SetValue: {full_path} = {value}")
            return True
        except Exception as e:
            self.logger.error(f" Failed to set {object_path}.{attribute} = {value}: {e}")
            return False
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
