#!/usr/bin/env python3
"""
AAS Client for Asset Administration Shell communication
Handles communication with AAS server to fetch ML model metadata
"""

import logging
import requests
import json
from typing import Dict, Any, Optional, List
from config.settings import config


class AASClient:
    """Client for communicating with AAS server"""

    def __init__(self, server_url: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.server_url = server_url or "http://10.3.1.52:8081"  # AAS server URL
        self.session = requests.Session()
        self.session.timeout = 10

        # AAS submodel path for production metadata
        self.production_metadata_path = "/submodels/aHR0cHM6Ly9leGFtcGxlLmNvbS9pZHMvc20vNTM5NF8xMTUyXzMwNDJfODQ4OQ/submodel-elements/CapacityParameters.Production"

    def connect(self) -> bool:
        """Test connection to AAS server"""
        try:
            test_url = f"{self.server_url}/shells"
            response = self.session.get(test_url, timeout=5)

            if response.status_code == 200:
                self.logger.debug(f"AAS server connection successful: {self.server_url}")
                return True
            else:
                self.logger.warning(f"AAS server returned status {response.status_code}")
                return False

        except requests.exceptions.RequestException as e:
            self.logger.warning(f"AAS server connection failed: {e}")
            return False

    def get_production_metadata(self) -> Optional[Dict[str, Any]]:
        """
        Fetch production metadata from AAS server
        Returns structured metadata with endpoint, input params, and output params
        """
        try:
            url = f"{self.server_url}{self.production_metadata_path}"
            self.logger.debug(f"Fetching production metadata from: {url}")

            response = self.session.get(url, timeout=self.session.timeout)

            if response.status_code == 200:
                raw_data = response.json()
                metadata = self._parse_production_metadata(raw_data)
                self.logger.info(f"✓ AAS metadata loaded: {len(metadata.get('input_parameters', []))} inputs, {len(metadata.get('output_parameters', []))} outputs")
                return metadata
            else:
                self.logger.error(f"AAS server returned {response.status_code} - Cannot proceed without AAS metadata")
                return None

        except requests.exceptions.RequestException as e:
            self.logger.error(f"AAS server request failed: {e} - Cannot proceed without AAS metadata")
            return None
        except (ValueError, KeyError) as e:
            self.logger.error(f"Error parsing AAS response: {e} - Cannot proceed without AAS metadata")
            return None

    def _parse_production_metadata(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse raw AAS production metadata into structured format"""
        metadata = {
            "ml_endpoint": None,
            "input_parameters": [],
            "output_parameters": [],
            "raw_data": raw_data
        }

        try:
            # Parse the SubmodelElementCollection
            if raw_data.get("modelType") == "SubmodelElementCollection":
                values = raw_data.get("value", [])

                for item in values:
                    id_short = item.get("idShort", "")

                    # Extract ML endpoint
                    if id_short == "Endpoint" and item.get("modelType") == "Property":
                        metadata["ml_endpoint"] = item.get("value", "")

                    # Extract input parameters
                    elif id_short == "In" and item.get("modelType") == "SubmodelElementCollection":
                        in_values = item.get("value", [])
                        for in_param in in_values:
                            if in_param.get("modelType") == "Property":
                                param_name = in_param.get("idShort", "")
                                if param_name:
                                    metadata["input_parameters"].append(param_name)

                    # Extract output parameters
                    elif id_short == "Out" and item.get("modelType") == "SubmodelElementCollection":
                        out_values = item.get("value", [])
                        for out_param in out_values:
                            if out_param.get("modelType") == "Property":
                                param_name = out_param.get("idShort", "")
                                if param_name:
                                    metadata["output_parameters"].append(param_name)

            self.logger.debug(f"Parsed metadata: {metadata}")
            return metadata

        except Exception as e:
            self.logger.error(f"Error parsing production metadata: {e}")
            return metadata



    def get_metadata_structure(self) -> Optional[Dict[str, Any]]:
        """
        Get structured metadata for ML model configuration
        Returns metadata in format compatible with existing config system
        """
        production_metadata = self.get_production_metadata()

        if not production_metadata:
            self.logger.warning("Could not fetch production metadata from AAS")
            return None

        # Convert to format compatible with existing metadata structure
        metadata_structure = {
            "ml_endpoint": production_metadata.get("ml_endpoint"),
            "part_attributes": production_metadata.get("input_parameters", []),
            "prediction_target": production_metadata.get("output_parameters", [None])[0],  # Take first output
            "station_mapping": {
                "Station1": {
                    "input_attributes": production_metadata.get("input_parameters", []),
                    "output_parameter": production_metadata.get("output_parameters", [None])[0]
                }
            }
        }

        self.logger.debug(f"Generated metadata structure: {metadata_structure}")
        return metadata_structure

    def get_ml_endpoint(self) -> Optional[str]:
        """Get ML service endpoint from AAS"""
        metadata = self.get_production_metadata()
        if metadata:
            endpoint = metadata.get("ml_endpoint")
            self.logger.debug(f"ML endpoint from AAS: {endpoint}")
            return endpoint
        return None

    def get_input_parameters(self) -> List[str]:
        """Get list of input parameters from AAS"""
        metadata = self.get_production_metadata()
        if metadata:
            params = metadata.get("input_parameters", [])
            self.logger.debug(f"Input parameters from AAS: {params}")
            return params
        return []

    def get_output_parameters(self) -> List[str]:
        """Get list of output parameters from AAS"""
        metadata = self.get_production_metadata()
        if metadata:
            params = metadata.get("output_parameters", [])
            self.logger.debug(f"Output parameters from AAS: {params}")
            return params
        return []

    def validate_aas_connection(self) -> Dict[str, Any]:
        """Validate AAS connection and metadata availability"""
        validation_result = {
            "connection_status": False,
            "metadata_available": False,
            "ml_endpoint_available": False,
            "input_params_count": 0,
            "output_params_count": 0,
            "errors": []
        }

        try:
            # Test connection
            validation_result["connection_status"] = self.connect()

            if validation_result["connection_status"]:
                # Test metadata fetch
                metadata = self.get_production_metadata()
                if metadata:
                    validation_result["metadata_available"] = True
                    validation_result["ml_endpoint_available"] = bool(metadata.get("ml_endpoint"))
                    validation_result["input_params_count"] = len(metadata.get("input_parameters", []))
                    validation_result["output_params_count"] = len(metadata.get("output_parameters", []))
                else:
                    validation_result["errors"].append("Could not fetch production metadata")
            else:
                validation_result["errors"].append("Could not connect to AAS server")

        except Exception as e:
            validation_result["errors"].append(f"Validation error: {str(e)}")

        return validation_result

    def disconnect(self):
        """Disconnect from AAS server"""
        if hasattr(self, 'session'):
            self.session.close()
        self.logger.debug("AAS client disconnected")

    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
