#!/usr/bin/env python3
"""
Test the regex pattern fix for product extraction
"""

import re

def test_current_regex():
    """Test the current regex pattern"""
    contents_list_str = "[*.MUs.Part:220]"
    
    print("=== Testing Current Regex ===")
    print(f"Input: {contents_list_str}")
    
    # Current pattern (doesn't work)
    current_pattern = r'Part:(\d+)'
    current_matches = re.findall(current_pattern, contents_list_str)
    print(f"Current pattern '{current_pattern}': {current_matches}")
    
    # Fixed pattern (should work)
    fixed_pattern = r'\.MUs\.Part:(\d+)'
    fixed_matches = re.findall(fixed_pattern, contents_list_str)
    print(f"Fixed pattern '{fixed_pattern}': {fixed_matches}")
    
    # Alternative pattern (more flexible)
    alt_pattern = r'Part:(\d+)'
    alt_matches = re.findall(alt_pattern, contents_list_str)
    print(f"Alternative pattern '{alt_pattern}': {alt_matches}")
    
    # Most flexible pattern
    flexible_pattern = r'(\w+):(\d+)'
    flexible_matches = re.findall(flexible_pattern, contents_list_str)
    print(f"Flexible pattern '{flexible_pattern}': {flexible_matches}")

def extract_product_info_fixed(contents_list_str: str) -> list:
    """
    Fixed version of extract_product_info
    """
    products = []
    
    if contents_list_str and isinstance(contents_list_str, str):
        # Look for patterns like "*.MUs.Part:220" or just "Part:220"
        part_matches = re.findall(r'Part:(\d+)', contents_list_str)
        
        for part_number in part_matches:
            products.append({
                'id': f"Part:{part_number}",
                'name': f"Part:{part_number}"
            })
    
    return products

if __name__ == "__main__":
    test_current_regex()
    
    print("\n=== Testing Fixed Function ===")
    test_input = "[*.MUs.Part:220]"
    result = extract_product_info_fixed(test_input)
    print(f"Input: {test_input}")
    print(f"Result: {result}")