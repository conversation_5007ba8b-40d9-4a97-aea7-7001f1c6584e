"""
Technomatix Monitor Module
Handles monitoring of Technomatix software for product arrival and status checking.
"""

import win32com.client
import pythoncom
import logging
import time
from typing import Optional, Dict, Any, Tuple
from enum import Enum
from .config import Config

class StationStatus(Enum):
    """Enumeration for station status"""
    EMPTY = "empty"
    PRODUCT_ARRIVED = "product_arrived"
    PROCESSING = "processing"
    UNKNOWN = "unknown"

class TechnomatixMonitor:
    """Monitor for Technomatix Plant Simulation software"""

    def __init__(self, model_path: Optional[str] = None):
        """
        Initialize Technomatix Monitor

        Args:
            model_path: Path to the Technomatix simulation model (uses config if None)
        """
        self.config = Config()
        self.model_path = model_path or self.config.MODEL_PATH
        self.ps = None
        self.logger = logging.getLogger(__name__)
        self.is_connected = False

        # Station monitoring configuration from config
        self.station1_path = self.config.STATION1_PATH
        self.station1_proc_time_attr = self.config.STATION1_PROC_TIME_ATTR
        self.event_controller_path = self.config.EVENT_CONTROLLER_PATH
        self.station1_contents_list_attr = self.config.STATION1_CONTENTS_LIST_ATTR
        self.station1_num_mu_attr = self.config.STATION1_NUM_MU_ATTR
    
    def connect(self) -> bool:
        """
        Connect to Technomatix Plant Simulation and load model

        Returns:
            True if connection successful, False otherwise
        """
        try:
            pythoncom.CoInitialize()
            self.ps = win32com.client.Dispatch("Tecnomatix.PlantSimulation.RemoteControl")
            self.ps.SetTrustModels(True)

            # Always load the model (required for proper operation)
            if not self.model_path:
                raise ValueError("Model path is required for proper operation")

            self.logger.info(f"Loading model: {self.model_path}")
            self.ps.LoadModel(self.model_path)
            self.ps.SetVisible(True)

            self.is_connected = True
            self.logger.info("Successfully connected to Technomatix and loaded model")
            return True
        except Exception as e:
            self.logger.error(f"Failed to connect to Technomatix: {e}")
            self.is_connected = False
            return False

    def start_simulation(self) -> bool:
        """
        Start the simulation using EventController

        Returns:
            True if simulation started successfully, False otherwise
        """
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return False

        try:
            # Use EventController to start simulation
            # Use SimTalk command to call the start method (no parameters for v15.1)
            simtalk_cmd = f"{self.event_controller_path}.start"
            self.ps.ExecuteSimTalk(simtalk_cmd)
            self.logger.info(f"Simulation started via SimTalk: {simtalk_cmd}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to start simulation: {e}")
            return False

    def stop_simulation(self) -> bool:
        """
        Stop the simulation using EventController

        Returns:
            True if simulation stopped successfully, False otherwise
        """
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return False

        try:
            # Use EventController to stop simulation
            # Use SimTalk command to call the stop method
            simtalk_cmd = f"{self.event_controller_path}.stop"
            self.ps.ExecuteSimTalk(simtalk_cmd)
            self.logger.info(f"Simulation stopped via SimTalk: {simtalk_cmd}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to stop simulation: {e}")
            return False

    def reset_simulation(self) -> bool:
        """
        Reset the simulation using EventController

        Returns:
            True if simulation reset successfully, False otherwise
        """
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return False

        try:
            # Use EventController to reset simulation
            # Use SimTalk command to call the reset method
            simtalk_cmd = f"{self.event_controller_path}.reset"
            self.ps.ExecuteSimTalk(simtalk_cmd)
            self.logger.info(f"Simulation reset via SimTalk: {simtalk_cmd}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to reset simulation: {e}")
            return False

    def disconnect(self):
        """Disconnect from Technomatix"""
        if self.ps:
            try:
                # Stop simulation before disconnecting
                self.stop_simulation()
                # Note: Plant Simulation doesn't have an explicit disconnect method
                # The connection will be closed when the object is destroyed
                self.ps = None
                self.is_connected = False
                self.logger.info("Disconnected from Technomatix")
            except Exception as e:
                self.logger.error(f"Error during disconnect: {e}")
    
    def get_station1_status(self) -> StationStatus:
        """
        Check the status of Station1 to determine if a product has arrived

        Returns:
            StationStatus enum indicating the current status
        """
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return StationStatus.UNKNOWN

        try:
            # Primary approach: Check contentsList for parts
            try:
                contents_path = f"{self.station1_path}.{self.station1_contents_list_attr}"
                contents = self.ps.GetValue(contents_path)

                # contentsList returns a collection/list of objects
                if contents and hasattr(contents, 'Count'):
                    part_count = contents.Count
                    if part_count > 0:
                        self.logger.info(f"Station1 has {part_count} part(s) - product detected")
                        return StationStatus.PRODUCT_ARRIVED
                    else:
                        self.logger.debug("Station1 contentsList is empty")
                        return StationStatus.EMPTY
                elif isinstance(contents, (list, tuple)) and len(contents) > 0:
                    self.logger.info(f"Station1 has {len(contents)} part(s) - product detected")
                    return StationStatus.PRODUCT_ARRIVED
                elif isinstance(contents, str) and contents.strip():
                    # Check if the string contains part information
                    import re
                    if re.search(r'Part:\d+', contents):
                        self.logger.info(f"Station1 has part(s) in string - product detected: {contents}")
                        return StationStatus.PRODUCT_ARRIVED
                    elif contents.strip() in ['[]', '', 'None', 'null']:
                        self.logger.debug("Station1 contentsList string indicates empty")
                        return StationStatus.EMPTY
                    else:
                        self.logger.debug(f"Station1 contentsList string but no parts detected: {contents}")
                        return StationStatus.EMPTY
                else:
                    self.logger.debug("Station1 contentsList indicates no parts")
                    return StationStatus.EMPTY

            except Exception as e:
                self.logger.debug(f"Could not check contentsList: {e}")

            # Fallback approach: Check numMU (Number of Material Units)
            try:
                num_mu_path = f"{self.station1_path}.{self.station1_num_mu_attr}"
                num_mu = self.ps.GetValue(num_mu_path)
                if isinstance(num_mu, (int, float)) and num_mu > 0:
                    self.logger.info(f"Station1 has {num_mu} material units - product detected")
                    return StationStatus.PRODUCT_ARRIVED
            except Exception as e:
                self.logger.debug(f"Could not check numMU: {e}")

            # If we can't determine status, assume empty
            self.logger.debug("Could not determine station status, assuming empty")
            return StationStatus.EMPTY

        except Exception as e:
            self.logger.error(f"Error checking Station1 status: {e}")
            return StationStatus.UNKNOWN
    
    def wait_for_product_arrival(self, check_interval: float = 1.0, timeout: Optional[float] = None) -> bool:
        """
        Wait for a product to arrive at Station1
        
        Args:
            check_interval: Time between status checks in seconds
            timeout: Maximum time to wait in seconds (None for infinite)
            
        Returns:
            True if product arrived, False if timeout or error
        """
        self.logger.info("Waiting for product arrival at Station1...")
        start_time = time.time()
        
        while True:
            status = self.get_station1_status()
            
            if status == StationStatus.PRODUCT_ARRIVED:
                self.logger.info("Product arrived at Station1!")
                return True
            elif status == StationStatus.UNKNOWN:
                self.logger.error("Unknown status - stopping wait")
                return False
            
            # Check timeout
            if timeout and (time.time() - start_time) > timeout:
                self.logger.warning(f"Timeout waiting for product arrival ({timeout}s)")
                return False
            
            time.sleep(check_interval)
    
    def update_processing_time(self, new_processing_time: float) -> bool:
        """
        Update the processing time for Station1
        
        Args:
            new_processing_time: New processing time value
            
        Returns:
            True if update successful, False otherwise
        """
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return False
        
        try:
            proc_time_path = f"{self.station1_path}.{self.station1_proc_time_attr}"
            
            # Get current value for logging
            current_val = self.ps.GetValue(proc_time_path)
            self.logger.info(f"Updating processing time from {current_val} to {new_processing_time}")
            
            # Set new value
            self.ps.SetValue(proc_time_path, new_processing_time)
            
            # Verify the update
            confirmed_val = self.ps.GetValue(proc_time_path)
            if abs(confirmed_val - new_processing_time) < 0.001:  # Allow for floating point precision
                self.logger.info(f"Successfully updated processing time to {confirmed_val}")
                return True
            else:
                self.logger.error(f"Update verification failed: expected {new_processing_time}, got {confirmed_val}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to update processing time: {e}")
            return False



    def get_station_info(self) -> Dict[str, Any]:
        """
        Get comprehensive information about Station1

        Returns:
            Dictionary with station information
        """
        info = {
            "connected": self.is_connected,
            "status": StationStatus.UNKNOWN,
            "processing_time": None,
            "attributes": {}
        }

        if not self.is_connected:
            return info

        try:
            # Get status
            info["status"] = self.get_station1_status()

            # Get processing time
            proc_time_path = f"{self.station1_path}.{self.station1_proc_time_attr}"
            info["processing_time"] = self.ps.GetValue(proc_time_path)

            # Try to get various attributes
            for attr in [self.station1_contents_list_attr, self.station1_num_mu_attr]:
                try:
                    attr_path = f"{self.station1_path}.{attr}"
                    value = self.ps.GetValue(attr_path)

                    # Special handling for contentsList
                    if attr == self.station1_contents_list_attr and value:
                        if hasattr(value, 'Count'):
                            info["attributes"][f'{attr}_count'] = value.Count
                            info["attributes"][f'{attr}_type'] = str(type(value))
                            # Try to get first item details
                            if value.Count > 0:
                                try:
                                    first_item = value.Item(1)  # COM collections are 1-indexed
                                    info["attributes"][f'{attr}_first_item'] = str(first_item)
                                except:
                                    pass
                        else:
                            info["attributes"][attr] = str(value)
                    else:
                        info["attributes"][attr] = value

                except Exception as e:
                    info["attributes"][attr] = f"Error: {e}"

        except Exception as e:
            self.logger.error(f"Error getting station info: {e}")

        return info
    
    def save_model(self) -> bool:
        """
        Save the Technomatix model
        
        Returns:
            True if save successful, False otherwise
        """
        if not self.is_connected:
            self.logger.error("Not connected to Technomatix")
            return False
        
        try:
            self.ps.SaveModel(self.model_path)
            self.logger.info("Model saved successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to save model: {e}")
            return False
    
    def get_station_info(self) -> Dict[str, Any]:
        """
        Get comprehensive information about Station1
        
        Returns:
            Dictionary with station information
        """
        info = {
            "connected": self.is_connected,
            "status": StationStatus.UNKNOWN,
            "processing_time": None,
            "attributes": {}
        }
        
        if not self.is_connected:
            return info
        
        try:
            # Get status
            info["status"] = self.get_station1_status()
            
            # Get processing time
            proc_time_path = f"{self.station1_path}.{self.station1_proc_time_attr}"
            info["processing_time"] = self.ps.GetValue(proc_time_path)
            
            # Try to get various attributes
            for attr in [self.station1_contents_list_attr, self.station1_num_mu_attr]:
                try:
                    attr_path = f"{self.station1_path}.{attr}"
                    value = self.ps.GetValue(attr_path)

                    # Special handling for contentsList
                    if attr == self.station1_contents_list_attr and value:
                        if hasattr(value, 'Count'):
                            info["attributes"][f'{attr}_count'] = value.Count
                            info["attributes"][f'{attr}_type'] = str(type(value))
                            # Try to get first item details
                            if value.Count > 0:
                                try:
                                    first_item = value.Item(1)  # COM collections are 1-indexed
                                    info["attributes"][f'{attr}_first_item'] = str(first_item)
                                except:
                                    pass
                        else:
                            info["attributes"][attr] = str(value)
                    else:
                        info["attributes"][attr] = value

                except Exception as e:
                    info["attributes"][attr] = f"Error: {e}"
                    
        except Exception as e:
            self.logger.error(f"Error getting station info: {e}")
        
        return info


# Example usage and testing
if __name__ == "__main__":
    # Configure logging for testing
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Model path - adjust as needed
    model_path = r"C:\Users\<USER>\aas\simulation_technmatix\html_start1.spp"
    
    # Create monitor
    monitor = TechnomatixMonitor(model_path)
    
    # Test connection
    print("Testing Technomatix connection...")
    if monitor.connect():
        print("✓ Connected to Technomatix")
        
        # Get station info
        info = monitor.get_station_info()
        print(f"Station1 Info: {info}")
        
        # Test status check
        status = monitor.get_station1_status()
        print(f"Station1 Status: {status}")
        
        # Disconnect
        monitor.disconnect()
    else:
        print("✗ Failed to connect to Technomatix")
