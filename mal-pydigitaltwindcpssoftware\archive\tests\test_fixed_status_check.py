#!/usr/bin/env python3
"""
Test the fixed status check logic
"""

import sys
import os
import re

# Add the main directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from main.technomatix_monitor import TechnomatixMonitor, StationStatus
from main.config import Config

def fixed_get_station1_status(monitor) -> StationStatus:
    """
    Fixed version of get_station1_status that handles string contentsList
    """
    if not monitor.is_connected:
        return StationStatus.UNKNOWN

    try:
        # Primary approach: Check contentsList for parts
        try:
            contents_path = f"{monitor.station1_path}.{monitor.station1_contents_list_attr}"
            contents = monitor.ps.GetValue(contents_path)

            # Case 1: contentsList returns a collection/list of objects with Count
            if contents and hasattr(contents, 'Count'):
                part_count = contents.Count
                if part_count > 0:
                    print(f" Found {part_count} part(s) via Count attribute")
                    return StationStatus.PRODUCT_ARRIVED
                else:
                    print(" Count is 0")
                    return StationStatus.EMPTY
                    
            # Case 2: contentsList returns a Python list/tuple
            elif isinstance(contents, (list, tuple)) and len(contents) > 0:
                print(f" Found {len(contents)} part(s) via list/tuple")
                return StationStatus.PRODUCT_ARRIVED
                
            # Case 3: contentsList returns a string (THIS IS THE MISSING CASE!)
            elif isinstance(contents, str) and contents.strip():
                # Check if the string contains part information
                if re.search(r'Part:\d+', contents):
                    print(f" Found part(s) in string: {contents}")
                    return StationStatus.PRODUCT_ARRIVED
                elif contents.strip() in ['[]', '', 'None', 'null']:
                    print(f" Empty string indicator: {contents}")
                    return StationStatus.EMPTY
                else:
                    print(f"⚠ Non-empty string but no parts detected: {contents}")
                    return StationStatus.EMPTY
            else:
                print(f" contentsList indicates no parts: {contents} (type: {type(contents)})")
                return StationStatus.EMPTY

        except Exception as e:
            print(f"Could not check contentsList: {e}")

        # Fallback approach: Check numMU (Number of Material Units)
        try:
            num_mu_path = f"{monitor.station1_path}.{monitor.station1_num_mu_attr}"
            num_mu = monitor.ps.GetValue(num_mu_path)
            if isinstance(num_mu, (int, float)) and num_mu > 0:
                print(f" Found {num_mu} material units via numMU")
                return StationStatus.PRODUCT_ARRIVED
        except Exception as e:
            print(f"Could not check numMU: {e}")

        # If we can't determine status, assume empty
        print("Could not determine station status, assuming empty")
        return StationStatus.EMPTY

    except Exception as e:
        print(f"Error checking Station1 status: {e}")
        return StationStatus.UNKNOWN

def test_fixed_status():
    """Test the fixed status check"""
    print("=== Testing Fixed Status Check ===")
    
    monitor = TechnomatixMonitor()
    
    try:
        print("1. Connecting...")
        if not monitor.connect():
            print(" Failed to connect")
            return
        print(" Connected")
        
        print("\n2. Starting simulation...")
        monitor.start_simulation()
        print(" Started")
        
        print("\n3. Testing ORIGINAL status check...")
        original_status = monitor.get_station1_status()
        print(f"Original result: {original_status}")
        
        print("\n4. Testing FIXED status check...")
        fixed_status = fixed_get_station1_status(monitor)
        print(f"Fixed result: {fixed_status}")
        
        print(f"\n5. Comparison:")
        print(f"   Original: {original_status}")
        print(f"   Fixed:    {fixed_status}")
        
        if original_status != fixed_status:
            print(" FIXED! The status detection is now working!")
        else:
            print(" Same result - need to investigate further")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        monitor.disconnect()

if __name__ == "__main__":
    test_fixed_status()
