#!/usr/bin/env python3
"""
Configuration for Technomatix-AAS-ML Integration System
"""

import os
from typing import List


class Config:
    """Main configuration class"""

    # Technomatix Configuration
    MODEL_PATH = r"C:\Users\<USER>\aas\SIMULATION_TECHNMATIX\html_start1.spp"
    STATION_NAME = "Station1"
    PRODUCT_CHECK_INTERVAL = 1.0  # seconds between product checks

    # ML Service Configuration (from AAS metadata)
    ML_PREDICTION_ENDPOINT = "/predict"
    ML_REQUEST_TIMEOUT = 10  # seconds
    ML_RETRY_ATTEMPTS = 2

    # Technomatix Station Paths
    STATION1_PATH = ".Models.Model.Station1"
    STATION1_PROC_TIME_ATTR = "ProcTime"
    STATION1_CONTENTS_LIST_ATTR = "contentsList"
    STATION1_NUM_MU_ATTR = "numMU"



    # Logging Configuration
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


    @classmethod
    def validate(cls) -> List[str]:
        """Validate configuration settings"""
        errors = []

        # Check if model file exists
        if not os.path.exists(cls.MODEL_PATH):
            errors.append(f"Model file not found: {cls.MODEL_PATH}")

        return errors


# Create global config instance
config = Config()
