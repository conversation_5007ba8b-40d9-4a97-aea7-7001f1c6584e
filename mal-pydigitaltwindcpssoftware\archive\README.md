# Technomatix-AAS Integration

A Python application that integrates Technomatix Plant Simulation with Asset Administration Shell (AAS) servers to provide real-time processing time updates based on production data.

## Features

- **Real-time Product Monitoring**: Tracks products flowing through Technomatix Station1
- **AAS Integration**: Retrieves processing times from AAS server endpoints
- **Dynamic Updates**: Updates Technomatix processing times based on AAS data
- **Product Counter**: Tracks total products processed from simulation start
- **Configurable**: Easy configuration through config file
- **Robust Error Handling**: Fallback mechanisms for network issues

## Project Structure

```
SIMULATION_TECHNMATIX/
├── main/                          # Main application code
│   ├── config.py                  # Configuration settings
│   ├── technomatix_monitor.py     # Technomatix interface
│   ├── aas_client.py             # AAS server communication
│   └── html_start1.spp           # Technomatix model file
├── tests/                         # Individual test scripts
│   ├── test_aas_connection.py     # Test A: AAS server connection
│   ├── test_technomatix_connection.py  # Test B: Technomatix connection
│   └── test_technomatix_update.py     # Test C: Technomatix updates
├── main.py                        # Main application entry point
├── requirements.txt               # Python dependencies
└── README.md                      # This file
```

## Installation

1. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Ensure Technomatix is installed** and accessible via COM interface

3. **Configure the application** by editing `main/config.py`

## Configuration

Edit `main/config.py` to customize for your environment:

### AAS Server Settings
```python
AAS_HOST = "*********"
AAS_PORT = 8081
AAS_PRODUCTION_TIME_URL = "http://*********:8081/submodels/.../ProductionTime/$value"
```

### Technomatix Settings
```python
MODEL_PATH = r"C:\Users\<USER>\html_start1.spp"
STATION1_PATH = ".Models.Model.Station1"
```

### Simulation Settings
```python
SIMULATION_MONITOR_TIME = 300  # 5 minutes
PRODUCT_CHECK_INTERVAL = 1.0   # Check every 1 second
```

## Usage

### Run Main Application
```bash
python main.py
```

### Run Individual Tests

**Test A: AAS Server Connection**
```bash
python tests/test_aas_connection.py
```

**Test B: Technomatix Connection**
```bash
python tests/test_technomatix_connection.py
```

**Test C: Technomatix Updates**
```bash
python tests/test_technomatix_update.py
```

## How It Works

1. **Connects to Technomatix** and loads the simulation model
2. **Tests AAS server** connectivity and data retrieval
3. **Starts simulation** using EventController
4. **Monitors Station1** for product arrivals
5. **Tracks unique products** with counter and names
6. **Retrieves processing times** from AAS server
7. **Updates Technomatix** with new processing times
8. **Displays progress** in real-time table format

## Sample Output

```
=== Technomatix-AAS Integration ===
Product Counter | Product Name | Status | Processing Time
------------------------------------------------------------
#001          | Part:245     | ARRIVED | [0.1s]
#001          | Part:245     | UPDATED | 55555.0 sec
#002          | Part:246     | ARRIVED | [6.2s]
#002          | Part:246     | UPDATED | 55555.0 sec
```

## Troubleshooting

### Common Issues

1. **Technomatix Connection Failed**
   - Ensure Technomatix is installed and running
   - Check model path in config.py
   - Verify COM interface is available

2. **AAS Server Not Reachable**
   - Check network connectivity
   - Verify AAS server URL and port
   - Application will use fallback values if AAS unavailable

3. **No Products Detected**
   - Ensure simulation is started in Technomatix
   - Check Station1 path configuration
   - Verify model has products flowing through Station1

### Configuration Validation

The application validates configuration on startup and reports any issues.

## Requirements

- **Python 3.8+**
- **Windows OS** (for Technomatix COM interface)
- **Technomatix Plant Simulation** installed
- **Network access** to AAS server

## License

This project is for internal use and integration with Technomatix and AAS systems.